#pragma once

#include <QDialog>

QT_BEGIN_NAMESPACE
class QDialogButtonBox;
QT_END_NAMESPACE

namespace Ui {
class ConfirmationDialog;
}

/**
 * @brief Dialog for confirming destructive operations
 * 
 * This dialog requires the user to type "YES" to confirm
 * potentially destructive operations like formatting or
 * firmware flashing.
 */
class ConfirmationDialog : public QDialog {
    Q_OBJECT

public:
    explicit ConfirmationDialog(const QString& warningMessage, QWidget *parent = nullptr);
    ~ConfirmationDialog();

private slots:
    void onTextChanged(const QString& text);

private:
    Ui::ConfirmationDialog *ui;
};
