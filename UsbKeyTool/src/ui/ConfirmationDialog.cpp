#include "ConfirmationDialog.h"
#include "ui_ConfirmationDialog.h" // This will be generated by `uic`
#include <QDialogButtonBox>
#include <QPushButton>

ConfirmationDialog::ConfirmationDialog(const QString& warningMessage, QWidget *parent) :
    QDialog(parent),
    ui(new Ui::ConfirmationDialog) // This is where the .ui file is instantiated
{
    ui->setupUi(this);
    setWindowTitle("Confirm Action");
    ui->warningLabel->setText(warningMessage);

    // US-6: Safeguard - disable confirm button initially
    ui->buttonBox->button(QDialogButtonBox::Ok)->setEnabled(false);
    ui->buttonBox->button(QDialogButtonBox::Ok)->setText("Confirm");

    connect(ui->lineEdit, &QLineEdit::textChanged, this, &ConfirmationDialog::onTextChanged);
}

ConfirmationDialog::~ConfirmationDialog() {
    delete ui;
}

void ConfirmationDialog::onTextChanged(const QString& text) {
    // US-6: Enable button only if user types "YES"
    ui->buttonBox->button(QDialogButtonBox::Ok)->setEnabled(text == "YES");
}
