#pragma once

#include "IDeviceBackend.h"
#include <Solid/Device>
#include <memory>
#include <libusb-1.0/libusb.h>

/**
 * @brief Factory class for creating device backends
 * 
 * This class analyzes USB devices and creates the appropriate
 * backend implementation based on the device's capabilities
 * and supported protocols.
 */
class BackendFactory {
public:
    /**
     * @brief Create a backend for the specified device
     * @param device The Solid device to create a backend for
     * @return Unique pointer to the created backend, or nullptr if no suitable backend found
     */
    static std::unique_ptr<IDeviceBackend> createBackend(const Solid::Device& device);

private:
    /**
     * @brief Check if a device has DFU interface
     * @param vid Vendor ID
     * @param pid Product ID
     * @return true if the device has a DFU interface
     */
    static bool hasDfuInterface(quint16 vid, quint16 pid);
};
