#pragma once

#include <QString>
#include <functional>

/**
 * @brief Interface for device-specific backend implementations
 * 
 * This interface defines the common operations that can be performed
 * on USB devices. Different backends implement device-specific protocols
 * like DFU, mass storage, or vendor-specific commands.
 */
class IDeviceBackend {
public:
    virtual ~IDeviceBackend() = default;
    
    /**
     * @brief Supported device features
     */
    enum class Feature {
        FlashFirmware,  ///< Can flash firmware to the device
        DumpFirmware,   ///< Can dump firmware from the device
        Format          ///< Can format the device storage
    };
    
    /**
     * @brief Progress callback function type
     * @param percentage Progress percentage (0-100)
     * @param message Status message
     */
    using ProgressCallback = std::function<void(int percentage, const QString& message)>;
    
    /**
     * @brief Get the backend name
     * @return Human-readable backend name
     */
    virtual QString name() const = 0;
    
    /**
     * @brief Check if the backend supports a specific feature
     * @param feature The feature to check
     * @return true if the feature is supported
     */
    virtual bool supports(Feature feature) const = 0;
    
    /**
     * @brief Flash firmware to the device
     * @param filePath Path to the firmware file
     * @param progress Progress callback function
     * @return true if the operation was successful
     */
    virtual bool flashFirmware(const QString& filePath, ProgressCallback progress) = 0;
    
    /**
     * @brief Dump firmware from the device
     * @param savePath Path where to save the dumped firmware
     * @param progress Progress callback function
     * @return true if the operation was successful
     */
    virtual bool dumpFirmware(const QString& savePath, ProgressCallback progress) = 0;
};
