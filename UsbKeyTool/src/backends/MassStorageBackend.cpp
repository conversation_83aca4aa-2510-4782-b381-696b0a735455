#include "MassStorageBackend.h"
#include <QDebug>

bool MassStorageBackend::supports(Feature feature) const {
    switch(feature) {
        case Feature::Format:
            // The backend announces support, but the logic is in DeviceContext via UDisks2
            return true;
        default:
            return false;
    }
}

bool MassStorageBackend::flashFirmware(const QString& filePath, ProgressCallback progress) {
    // This is where vendor-specific flashing would be implemented,
    // for example, by sending special SCSI or ATA commands to the device
    // to switch it into a programming mode (e.g., Phison, SMI, Alcor controllers).
    // This is highly specialized and out of scope for the skeleton.
    Q_UNUSED(filePath)
    Q_UNUSED(progress)
    qWarning("Firmware flashing for generic mass storage devices is not supported.");
    return false;
}

bool MassStorageBackend::dumpFirmware(const QString& savePath, ProgressCallback progress) {
    Q_UNUSED(savePath)
    Q_UNUSED(progress)
    qWarning("Firmware dumping for generic mass storage devices is not supported.");
    return false;
}
