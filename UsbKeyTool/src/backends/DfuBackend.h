#pragma once
#include "IDeviceBackend.h"
#include <cstdint>

/**
 * @brief Backend for devices supporting the DFU (Device Firmware Update) protocol
 * 
 * This backend implements the USB DFU specification for flashing
 * and dumping firmware from compatible devices.
 */
class DfuBackend : public IDeviceBackend {
public:
    explicit DfuBackend(uint16_t vid, uint16_t pid);
    ~DfuBackend() override = default;

    QString name() const override { return "DFU"; }
    bool supports(Feature feature) const override;
    
    bool flashFirmware(const QString& filePath, ProgressCallback progress) override;
    bool dumpFirmware(const QString& savePath, ProgressCallback progress) override;

private:
    uint16_t m_vid;
    uint16_t m_pid;
};
