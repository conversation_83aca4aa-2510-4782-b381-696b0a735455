#pragma once
#include "IDeviceBackend.h"

/**
 * @brief Backend for mass storage USB devices
 * 
 * This backend handles operations on USB mass storage devices,
 * primarily focusing on formatting operations through UDisks2.
 */
class MassStorageBackend : public IDeviceBackend {
public:
    MassStorageBackend() = default;
    ~MassStorageBackend() override = default;

    QString name() const override { return "Mass Storage"; }
    bool supports(Feature feature) const override;
    
    bool flashFirmware(const QString& filePath, ProgressCallback progress) override;
    bool dumpFirmware(const QString& savePath, ProgressCallback progress) override;
};
