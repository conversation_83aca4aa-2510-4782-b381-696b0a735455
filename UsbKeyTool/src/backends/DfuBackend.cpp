#include "DfuBackend.h"
#include "../core/UsbHandle.h"
#include <QFile>
#include <QDebug>
#include <libusb-1.0/libusb.h>

// DFU constants from the spec
constexpr int DFU_DETACH = 0;
constexpr int DFU_DNLOAD = 1;
constexpr int DFU_UPLOAD = 2;
constexpr int DFU_GETSTATUS = 3;

DfuBackend::DfuBackend(uint16_t vid, uint16_t pid) : m_vid(vid), m_pid(pid) {}

bool DfuBackend::supports(Feature feature) const {
    switch(feature) {
        case Feature::FlashFirmware:
        case Feature::DumpFirmware:
            return true;
        case Feature::Format:
        default:
            return false;
    }
}

// US-3: DFU Flashing Logic
bool DfuBackend::flashFirmware(const QString& filePath, ProgressCallback progress) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open firmware file:" << filePath;
        return false;
    }
    QByteArray firmwareData = file.readAll();
    
    libusb_device_handle *handle = libusb_open_device_with_vid_pid(nullptr, m_vid, m_pid);
    if (!handle) {
        qWarning("Could not find/open DFU device.");
        return false;
    }
    // RAII will close the handle on scope exit
    // Note: A real implementation needs to find the DFU interface number first
    // and detach the kernel driver if necessary.
    libusb_claim_interface(handle, 0); // Assuming interface 0

    // Simplified DFU_DNLOAD sequence
    int bytes_sent = 0;
    int transaction = 0;
    while (bytes_sent < firmwareData.size()) {
        const int chunk_size = qMin(1024, firmwareData.size() - bytes_sent);
        QByteArray chunk = firmwareData.mid(bytes_sent, chunk_size);
        
        int ret = libusb_control_transfer(
            handle,
            0x21, // bmRequestType: Host-to-device, Class, Interface
            DFU_DNLOAD, // bRequest
            transaction++, // wValue: transaction number
            0, // wIndex: interface number
            (unsigned char*)chunk.data(),
            chunk.size(),
            5000 // timeout 5s
        );

        if (ret < 0) {
            qWarning() << "DFU_DNLOAD failed with code:" << ret;
            libusb_release_interface(handle, 0);
            libusb_close(handle);
            return false;
        }

        bytes_sent += chunk.size();
        progress( (bytes_sent * 100) / firmwareData.size(), "Flashing...");
        
        // TODO: Implement DFU_GETSTATUS polling to wait for device to be ready
        // for the next block. This is a crucial part of the state machine.
    }

    // Finalize the transfer (zero-length DNLOAD)
    libusb_control_transfer(handle, 0x21, DFU_DNLOAD, transaction++, 0, nullptr, 0, 5000);

    progress(100, "Flash complete. Device is resetting.");

    libusb_release_interface(handle, 0);
    libusb_close(handle);
    return true;
}

// US-2: Firmware dump
bool DfuBackend::dumpFirmware(const QString& savePath, ProgressCallback progress) {
    // DFU_UPLOAD is more complex and device-dependent. This is a stub.
    Q_UNUSED(savePath)
    Q_UNUSED(progress)
    qWarning("Firmware dumping via DFU (UPLOAD) is not implemented in this skeleton.");
    return false;
}
