#include "BackendFactory.h"
#include "DfuBackend.h"
#include "MassStorageBackend.h"
#include <Solid/StorageDrive>
#include <QDebug>

// A simple helper to check for DFU interfaces using libusb
bool BackendFactory::hasDfuInterface(quint16 vid, quint16 pid) {
    libusb_device **devs;
    ssize_t cnt = libusb_get_device_list(nullptr, &devs);
    if (cnt < 0) return false;

    bool found = false;
    for (ssize_t i = 0; i < cnt; ++i) {
        libusb_device_descriptor desc;
        if (libusb_get_device_descriptor(devs[i], &desc) != 0) continue;
        
        if (desc.idVendor == vid && desc.idProduct == pid) {
            libusb_config_descriptor *config;
            if (libusb_get_active_config_descriptor(devs[i], &config) == 0) {
                for (uint8_t j = 0; j < config->bNumInterfaces; ++j) {
                    const libusb_interface &interface = config->interface[j];
                    for (int k = 0; k < interface.num_altsetting; ++k) {
                        const libusb_interface_descriptor &if_desc = interface.altsetting[k];
                        // DFU class code is 0xFE, subclass 1
                        if (if_desc.bInterfaceClass == 0xFE && if_desc.bInterfaceSubClass == 1) {
                            found = true;
                            break;
                        }
                    }
                    if(found) break;
                }
                libusb_free_config_descriptor(config);
            }
        }
        if(found) break;
    }
    libusb_free_device_list(devs, 1);
    return found;
}


std::unique_ptr<IDeviceBackend> BackendFactory::createBackend(const Solid::Device& device) {
    if (!device.isValid()) {
        return nullptr;
    }
    
    quint16 vid = 0, pid = 0;
    if (device.isType(Solid::Device::StorageDrive)) {
        auto drive = device.as<Solid::StorageDrive>();
        vid = drive->vendor().toUShort(nullptr, 16);
        pid = drive->product().toUShort(nullptr, 16);
    }
    // More logic to get VID/PID for non-storage devices would be needed here.

    if (vid == 0 && pid == 0) return nullptr;

    // US-1: Check for DFU interface first
    if (hasDfuInterface(vid, pid)) {
        qDebug() << "Device" << device.udi() << "has a DFU interface, creating DfuBackend.";
        return std::make_unique<DfuBackend>(vid, pid);
    }

    // US-1: Check for Mass Storage interface
    if (device.isType(Solid::Device::StorageDrive)) {
        qDebug() << "Device" << device.udi() << "is a mass storage device, creating MassStorageBackend.";
        return std::make_unique<MassStorageBackend>();
    }

    qWarning() << "No suitable backend found for device" << device.udi();
    return nullptr;
}
