# --- Compiler Warnings
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic -Werror")
endif()

# --- Find External Dependencies via PkgConfig
find_package(PkgConfig REQUIRED)
pkg_check_modules(LIBUDEV REQUIRED libudev)
pkg_check_modules(LIBUSB REQUIRED libusb-1.0)
pkg_check_modules(UDISKS2 REQUIRED udisks2)

# --- Find KDE Frameworks 5 (Solid)
# Requires 'extra-cmake-modules' package
find_package(ECM REQUIRED NO_MODULE)
set(CMAKE_MODULE_PATH ${ECM_FIND_MODULE_DIR})
find_package(KF5 REQUIRED Solid)

# --- Find ChipID Database (usb.ids)
# This uses a custom Find module to locate the usb.ids file.
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/3rdparty")
find_package(ChipID REQUIRED)

# --- Define Executable and Sources
add_executable(${PROJECT_NAME}
    main.cpp

    core/Logger.cpp
    core/PermissionManager.cpp
    core/DeviceManager.cpp
    core/DeviceContext.cpp

    backends/BackendFactory.cpp
    backends/DfuBackend.cpp
    backends/MassStorageBackend.cpp

    ui/MainWindow.cpp
    ui/ConfirmationDialog.cpp
)

# --- Include Directories
target_include_directories(${PROJECT_NAME} PRIVATE
    "${CMAKE_CURRENT_SOURCE_DIR}"
    "${LIBUDEV_INCLUDE_DIRS}"
    "${LIBUSB_INCLUDE_DIRS}"
    "${KF5_INCLUDE_DIRS}"
)

# --- Link Libraries
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt5::Widgets
    Qt5::DBus
    ${LIBUDEV_LIBRARIES}
    ${LIBUSB_LIBRARIES}
    KF5::Solid
)

# --- Pass defines to source code
target_compile_definitions(${PROJECT_NAME} PRIVATE
    USB_IDS_PATH="${CHIPID_USB_IDS_FILE}"
)
