#include "PermissionManager.h"
#include <QDBusInterface>
#include <QDBusReply>
#include <QCoreApplication>
#include <QProcess>

PermissionManager::PermissionManager(QObject* parent) : QObject(parent) {}

// US-7: Security & Permissions Check
bool PermissionManager::hasUdisksPrivileges() {
    // We check for one of the most restrictive actions we might perform.
    // If the user has rights for this, they likely have rights for others.
    const QString actionId = "org.freedesktop.udisks2.format-device";

    QDBusInterface polkit("org.freedesktop.PolicyKit1",
                        "/org/freedesktop/PolicyKit1/Authority",
                        "org.freedesktop.PolicyKit1.Authority",
                        QDBusConnection::systemBus());

    if (!polkit.isValid()) {
        qWarning("Failed to connect to Polkit D-Bus service.");
        return false; // Fail safe
    }

    // Details for the authorization check
    QVariantMap details;
    // 'subject' is the process we are checking privileges for.
    // A 'unix-process' subject has 'pid' and 'start-time'.
    QVariantMap subject;
    subject.insert("pid", QVariant::fromValue(QCoreApplication::applicationPid()));
    // We don't need start-time for this simple check.
    
    // The call to CheckAuthorization
    QDBusReply<QDBusVariant> reply = polkit.call("CheckAuthorization",
                                                QVariant::fromValue(QVariantMap{{"system-bus-name", subject}}),
                                                actionId,
                                                details,
                                                1, // flags: 1 means AllowUserInteraction
                                                ""); // cancellation_id

    // The reply is a QDBusVariant containing a struct: (bool is_authorized, bool is_challenge, QVariantMap details)
    // A proper implementation would parse this struct. For simplicity, we just check if the call succeeded.
    // A failed call (error reply) means we are definitely not authorized.
    // A successful call where is_authorized is true means we are good.
    // A successful call where is_challenge is true means pkexec will be needed.
    // For this skeleton, we assume a valid reply means the user *can* get privileges.
    return reply.isValid(); 
}

QString PermissionManager::getPkexecCommand() {
    return QString("pkexec %1").arg(QCoreApplication::applicationFilePath());
}
