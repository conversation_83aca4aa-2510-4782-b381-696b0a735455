#include "DeviceContext.h"
#include "Logger.h"
#include "../backends/BackendFactory.h"
#include "../backends/IDeviceBackend.h"
#include <Solid/StorageDrive>
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>
#include <QDBusInterface>
#include <QDBusReply>
#include <QDesktopServices>
#include <QUrl>

DeviceContext::DeviceContext(const Solid::Device& device, QObject* parent)
    : QObject(parent), m_device(device) {
    
    // Create appropriate backend for this device
    m_backend = BackendFactory::createBackend(device);
    
    Logger::instance()->log(QString("DeviceContext created for device: %1").arg(device.udi()));
}

bool DeviceContext::supportsFeature(const QString& feature) const {
    if (!m_backend) {
        return false;
    }
    
    if (feature == "FlashFirmware") {
        return m_backend->supports(IDeviceBackend::Feature::FlashFirmware);
    } else if (feature == "DumpFirmware") {
        return m_backend->supports(IDeviceBackend::Feature::DumpFirmware);
    } else if (feature == "Format") {
        return m_backend->supports(IDeviceBackend::Feature::Format);
    }
    
    return false;
}

void DeviceContext::populateInfo() {
    m_deviceInfo.clear();
    
    if (!m_device.isValid()) {
        Logger::instance()->log("Cannot populate info for invalid device");
        return;
    }
    
    // Get basic device information from Solid
    m_deviceInfo["udi"] = m_device.udi();
    m_deviceInfo["description"] = m_device.description();
    m_deviceInfo["product"] = m_device.product();
    m_deviceInfo["vendor"] = m_device.vendor();
    
    if (m_device.isType(Solid::Device::StorageDrive)) {
        auto drive = m_device.as<Solid::StorageDrive>();
        if (drive) {
            m_deviceInfo["size"] = drive->size();
            m_deviceInfo["removable"] = drive->isRemovable();
            m_deviceInfo["hotpluggable"] = drive->isHotpluggable();
            
            // Try to extract VID/PID from vendor/product strings
            QString vendor = drive->vendor();
            QString product = drive->product();
            
            // This is a simplified approach - in a real implementation,
            // you would use libusb to get the actual VID/PID
            m_deviceInfo["vid"] = "N/A";
            m_deviceInfo["pid"] = "N/A";
            m_deviceInfo["serial"] = "N/A";
            m_deviceInfo["manufacturer"] = vendor.isEmpty() ? "Unknown" : vendor;
            m_deviceInfo["productName"] = product.isEmpty() ? "Unknown" : product;
        }
    }
    
    // Parse USB IDs for chipset information
    parseUsbIds();
    
    Logger::instance()->log("Device information populated");
    emit infoUpdated();
}

void DeviceContext::startFlashFirmware(const QString& filePath) {
    if (!m_backend || !m_backend->supports(IDeviceBackend::Feature::FlashFirmware)) {
        emit taskFinished(false, "Firmware flashing not supported for this device");
        return;
    }
    
    Logger::instance()->log(QString("Starting firmware flash: %1").arg(filePath));
    
    auto progressCallback = [this](int percentage, const QString& message) {
        emit progressUpdated(percentage, message);
    };
    
    bool success = m_backend->flashFirmware(filePath, progressCallback);
    QString message = success ? "Firmware flashed successfully" : "Firmware flashing failed";
    
    Logger::instance()->log(message);
    emit taskFinished(success, message);
}

void DeviceContext::startFormat(const QString& filesystem, const QString& label) {
    if (!m_backend || !m_backend->supports(IDeviceBackend::Feature::Format)) {
        emit taskFinished(false, "Formatting not supported for this device");
        return;
    }

    Logger::instance()->log(QString("Starting format: %1 with label '%2'").arg(filesystem, label));

    // Use UDisks2 D-Bus interface for formatting
    QDBusInterface udisks("org.freedesktop.UDisks2",
                         m_device.udi(),
                         "org.freedesktop.UDisks2.Block",
                         QDBusConnection::systemBus());

    if (!udisks.isValid()) {
        emit taskFinished(false, "Failed to connect to UDisks2 service");
        return;
    }

    // Prepare format options
    QVariantMap options;
    if (!label.isEmpty()) {
        options["label"] = label;
    }

    // Call the Format method
    QDBusReply<void> reply = udisks.call("Format", filesystem.toLower(), options);

    if (reply.isValid()) {
        Logger::instance()->log("Format operation completed successfully");
        emit taskFinished(true, "Device formatted successfully");

        // TODO: Implement auto-mount functionality here
        // This would involve listening for the new partition and mounting it
    } else {
        QString error = QString("Format failed: %1").arg(reply.error().message());
        Logger::instance()->log(error);
        emit taskFinished(false, error);
    }
}

void DeviceContext::parseUsbIds() {
    // This is a placeholder implementation
    // In a real implementation, you would parse the USB_IDS_PATH file
    // and look up the VID/PID to get human-readable names

    QString vid = m_deviceInfo.value("vid").toString();
    QString pid = m_deviceInfo.value("pid").toString();

    if (vid != "N/A" && pid != "N/A") {
        bool vidOk, pidOk;
        quint16 vidNum = vid.toUShort(&vidOk, 16);
        quint16 pidNum = pid.toUShort(&pidOk, 16);

        if (vidOk && pidOk) {
            QString chipsetName = lookupVendorProduct(vidNum, pidNum);
            if (!chipsetName.isEmpty()) {
                m_deviceInfo["chipset"] = chipsetName;
            }
        }
    }
}

QString DeviceContext::lookupVendorProduct(quint16 vid, quint16 pid) const {
    // Placeholder implementation
    // In a real implementation, this would parse the usb.ids file
    Q_UNUSED(vid)
    Q_UNUSED(pid)
    return "Unknown Chipset";
}

void DeviceContext::startDumpFirmware(const QString& savePath) {
    if (!m_backend || !m_backend->supports(IDeviceBackend::Feature::DumpFirmware)) {
        emit taskFinished(false, "Firmware dumping not supported for this device");
        return;
    }
    
    Logger::instance()->log(QString("Starting firmware dump to: %1").arg(savePath));
    
    auto progressCallback = [this](int percentage, const QString& message) {
        emit progressUpdated(percentage, message);
    };
    
    bool success = m_backend->dumpFirmware(savePath, progressCallback);
    QString message = success ? "Firmware dumped successfully" : "Firmware dumping failed";
    
    Logger::instance()->log(message);
    emit taskFinished(success, message);
}
