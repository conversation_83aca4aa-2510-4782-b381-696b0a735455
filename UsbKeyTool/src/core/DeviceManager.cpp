#include "DeviceManager.h"
#include "Logger.h"
#include <Solid/DeviceNotifier>
#include <Solid/StorageDrive>
#include <QDebug>

DeviceManager::DeviceManager(QObject* parent) 
    : QObject(parent), m_monitoring(false) {
    Logger::instance()->log("DeviceManager initialized");
}

QList<Solid::Device> DeviceManager::getUsbDevices() const {
    QList<Solid::Device> usbDevices;
    
    // Get all storage drives
    const QList<Solid::Device> allDevices = Solid::Device::listFromType(Solid::DeviceInterface::StorageDrive);
    
    for (const Solid::Device& device : allDevices) {
        if (device.isValid() && device.isType(Solid::Device::StorageDrive)) {
            auto drive = device.as<Solid::StorageDrive>();
            // Check if it's a USB device
            if (drive && (drive->bus() == Solid::StorageDrive::Usb)) {
                usbDevices.append(device);
                Logger::instance()->log(QString("Found USB device: %1").arg(device.udi()));
            }
        }
    }
    
    return usbDevices;
}

void DeviceManager::startMonitoring() {
    if (m_monitoring) {
        return;
    }
    
    Solid::DeviceNotifier* notifier = Solid::DeviceNotifier::instance();
    connect(notifier, &Solid::DeviceNotifier::deviceAdded,
            this, &DeviceManager::onDeviceAdded);
    connect(notifier, &Solid::DeviceNotifier::deviceRemoved,
            this, &DeviceManager::onDeviceRemoved);
    
    m_monitoring = true;
    Logger::instance()->log("Device monitoring started");
}

void DeviceManager::stopMonitoring() {
    if (!m_monitoring) {
        return;
    }
    
    Solid::DeviceNotifier* notifier = Solid::DeviceNotifier::instance();
    disconnect(notifier, &Solid::DeviceNotifier::deviceAdded,
               this, &DeviceManager::onDeviceAdded);
    disconnect(notifier, &Solid::DeviceNotifier::deviceRemoved,
               this, &DeviceManager::onDeviceRemoved);
    
    m_monitoring = false;
    Logger::instance()->log("Device monitoring stopped");
}

void DeviceManager::onDeviceAdded(const QString& udi) {
    Solid::Device device(udi);
    if (device.isValid() && device.isType(Solid::Device::StorageDrive)) {
        auto drive = device.as<Solid::StorageDrive>();
        if (drive && (drive->bus() == Solid::StorageDrive::Usb)) {
            Logger::instance()->log(QString("USB device added: %1").arg(udi));
            emit deviceAdded(device);
        }
    }
}

void DeviceManager::onDeviceRemoved(const QString& udi) {
    Logger::instance()->log(QString("Device removed: %1").arg(udi));
    emit deviceRemoved(udi);
}
