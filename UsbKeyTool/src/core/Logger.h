#pragma once

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QMutex>

/**
 * @brief Thread-safe singleton logger for the application
 * 
 * This class provides centralized logging functionality with
 * automatic timestamping and thread-safe access.
 */
class Logger : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Get the singleton instance
     * @return Pointer to the Logger instance
     */
    static Logger* instance();
    
    /**
     * @brief Log a message with timestamp
     * @param message The message to log
     */
    void log(const QString& message);

private:
    explicit Logger(QObject* parent = nullptr);
    ~Logger() override;
    
    // Disable copy constructor and assignment
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    static Logger* m_instance;
    QFile m_logFile;
    QTextStream m_logStream;
    QMutex m_mutex;
};
