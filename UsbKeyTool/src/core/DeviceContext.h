#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <Solid/Device>
#include <memory>

class IDeviceBackend;

/**
 * @brief Represents a single USB device and its operations
 * 
 * This class encapsulates all operations that can be performed
 * on a specific USB device, including information retrieval,
 * firmware operations, and storage formatting.
 */
class DeviceContext : public QObject {
    Q_OBJECT

public:
    explicit DeviceContext(const Solid::Device& device, QObject* parent = nullptr);
    
    /**
     * @brief Get the Solid device object
     * @return Reference to the Solid::Device
     */
    const Solid::Device& device() const { return m_device; }
    
    /**
     * @brief Get device information
     * @return Map containing device properties
     */
    QVariantMap getDeviceInfo() const { return m_deviceInfo; }
    
    /**
     * @brief Check if the device supports a specific feature
     * @param feature The feature to check
     * @return true if the feature is supported
     */
    bool supportsFeature(const QString& feature) const;

public slots:
    /**
     * @brief Populate device information from hardware
     */
    void populateInfo();
    
    /**
     * @brief Start firmware flashing operation
     * @param filePath Path to the firmware file
     */
    void startFlashFirmware(const QString& filePath);
    
    /**
     * @brief Start firmware dump operation
     * @param savePath Path where to save the dumped firmware
     */
    void startDumpFirmware(const QString& savePath);
    
    /**
     * @brief Start device formatting operation
     * @param filesystem The filesystem type (FAT32, exFAT, ext4)
     * @param label Volume label
     */
    void startFormat(const QString& filesystem, const QString& label);

signals:
    /**
     * @brief Emitted when device information is updated
     */
    void infoUpdated();
    
    /**
     * @brief Emitted to report progress of long-running operations
     * @param percentage Progress percentage (0-100)
     * @param message Status message
     */
    void progressUpdated(int percentage, const QString& message);
    
    /**
     * @brief Emitted when a task is completed
     * @param success true if the task completed successfully
     * @param message Result message
     */
    void taskFinished(bool success, const QString& message);

private:
    void parseUsbIds();
    QString lookupVendorProduct(quint16 vid, quint16 pid) const;
    
    Solid::Device m_device;
    QVariantMap m_deviceInfo;
    std::unique_ptr<IDeviceBackend> m_backend;
};
