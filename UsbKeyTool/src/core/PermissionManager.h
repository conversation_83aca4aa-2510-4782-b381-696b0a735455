#pragma once

#include <QObject>

/**
 * @brief Manages system permissions using Polkit
 * 
 * This class provides functionality to check and request
 * system-level permissions for USB device operations.
 */
class PermissionManager : public QObject {
    Q_OBJECT

public:
    explicit PermissionManager(QObject* parent = nullptr);
    
    /**
     * @brief Check if the current user has UDisks2 privileges
     * @return true if the user has the necessary permissions
     */
    bool hasUdisksPrivileges();
    
    /**
     * @brief Get the pkexec command to run the application with elevated privileges
     * @return Command string for pkexec
     */
    QString getPkexecCommand();
};
