#include "Logger.h"
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QDebug>

Logger* Logger::m_instance = nullptr;

Logger* Logger::instance() {
    // This is not thread-safe in C++11 and earlier, but QMutex handles it below.
    // For a robust singleton, std::call_once and std::once_flag would be ideal.
    if (!m_instance) {
        m_instance = new Logger();
    }
    return m_instance;
}

Logger::Logger(QObject* parent) : QObject(parent) {
    const QString logDir_path = QStandardPaths::writableLocation(QStandardPaths::LocalShareLocation);
    QDir logDir(logDir_path);
    if (!logDir.exists()) {
        logDir.mkpath(".");
    }

    m_logFile.setFileName(logDir.filePath("log.txt"));
    if (!m_logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        qWarning() << "Could not open log file for writing:" << m_logFile.errorString();
    } else {
        m_logStream.setDevice(&m_logFile);
        log("--- Log session started ---");
    }
}

Logger::~Logger() {
    log("--- Log session ended ---");
    if (m_logFile.isOpen()) {
        m_logFile.close();
    }
}

void Logger::log(const QString& message) {
    const QMutexLocker locker(&m_mutex);
    if (!m_logFile.isOpen()) {
        return;
    }
    
    // US-7: Log with millisecond timestamps
    const QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz");
    m_logStream << timestamp << " | " << message << "\n";
    m_logStream.flush(); // Ensure message is written immediately
}
