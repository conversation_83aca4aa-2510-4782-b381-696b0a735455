#pragma once

#include <libusb-1.0/libusb.h>
#include <memory>

/**
 * @brief RAII wrapper for libusb device handle
 * 
 * This class provides automatic cleanup of libusb device handles
 * to prevent resource leaks.
 */
class UsbHandle {
public:
    explicit UsbHandle(libusb_device_handle* handle = nullptr) 
        : m_handle(handle) {}
    
    ~UsbHandle() {
        if (m_handle) {
            libusb_close(m_handle);
        }
    }
    
    // Move constructor
    UsbHandle(UsbHandle&& other) noexcept 
        : m_handle(other.m_handle) {
        other.m_handle = nullptr;
    }
    
    // Move assignment
    UsbHandle& operator=(UsbHandle&& other) noexcept {
        if (this != &other) {
            if (m_handle) {
                libusb_close(m_handle);
            }
            m_handle = other.m_handle;
            other.m_handle = nullptr;
        }
        return *this;
    }
    
    // Delete copy constructor and assignment
    UsbHandle(const UsbHandle&) = delete;
    UsbHandle& operator=(const UsbHandle&) = delete;
    
    // Get the raw handle
    libusb_device_handle* get() const { return m_handle; }
    
    // Check if handle is valid
    bool isValid() const { return m_handle != nullptr; }
    
    // Release ownership of the handle
    libusb_device_handle* release() {
        libusb_device_handle* handle = m_handle;
        m_handle = nullptr;
        return handle;
    }
    
    // Reset with a new handle
    void reset(libusb_device_handle* handle = nullptr) {
        if (m_handle) {
            libusb_close(m_handle);
        }
        m_handle = handle;
    }

private:
    libusb_device_handle* m_handle;
};
