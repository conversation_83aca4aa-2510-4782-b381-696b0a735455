#pragma once

#include <QObject>
#include <QList>
#include <Solid/Device>

/**
 * @brief Manages USB device discovery and monitoring
 * 
 * This class uses KDE's Solid framework to discover and monitor
 * USB devices connected to the system.
 */
class DeviceManager : public QObject {
    Q_OBJECT

public:
    explicit DeviceManager(QObject* parent = nullptr);
    
    /**
     * @brief Get list of all connected USB devices
     * @return List of Solid::Device objects representing USB devices
     */
    QList<Solid::Device> getUsbDevices() const;
    
    /**
     * @brief Start monitoring for device changes
     */
    void startMonitoring();
    
    /**
     * @brief Stop monitoring for device changes
     */
    void stopMonitoring();

signals:
    /**
     * @brief Emitted when a new USB device is connected
     * @param device The newly connected device
     */
    void deviceAdded(const Solid::Device& device);
    
    /**
     * @brief Emitted when a USB device is disconnected
     * @param udi The UDI of the disconnected device
     */
    void deviceRemoved(const QString& udi);

private slots:
    void onDeviceAdded(const QString& udi);
    void onDeviceRemoved(const QString& udi);

private:
    bool m_monitoring;
};
