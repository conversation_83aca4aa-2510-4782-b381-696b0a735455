cmake_minimum_required(VERSION 3.24)

project(UsbKeyTool VERSION 1.0.0 LANGUAGES CXX)

# --- Compiler & Standard Settings
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# --- Qt5 Settings
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)
find_package(Qt5 REQUIRED COMPONENTS Widgets DBus)

# --- Project Structure
add_subdirectory(src)

# --- Installation Rules
set(INSTALL_PREFIX "/opt/${PROJECT_NAME}")

# Install the main executable
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION "${INSTALL_PREFIX}/bin"
)

# Install the .desktop file for application menus
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/data/com.github.YourName.UsbKeyTool.desktop"
    DESTINATION "/usr/share/applications"
)

# Install the application icon
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/data/icons/com.github.YourName.UsbKeyTool.svg"
    DESTINATION "/usr/share/icons/hicolor/scalable/apps"
)

# Install the polkit policy file for permissions
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/data/com.github.YourName.UsbKeyTool.policy"
    DESTINATION "/usr/share/polkit-1/actions"
)

message(STATUS "Project Name: ${PROJECT_NAME}")
message(STATUS "Install Prefix: ${INSTALL_PREFIX}")
message(STATUS "Build project in a 'build' directory: 'cmake -S . -B build'")
message(STATUS "Then build with: 'cmake --build build'")
message(STATUS "Install with (requires sudo): 'sudo cmake --install build'")
