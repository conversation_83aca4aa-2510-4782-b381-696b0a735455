You are agenieus C++, Qt5 engineer. Write the full prompt for generating a debian application for read,save,write firmware bin usb key firmware using IOCTL. The application is able to retrieve chipset version, device type, manufacturer and all the information of a USB key. The application is able to format and mount usb key drive. Use all usefull project (grom github or other source)for achieving the implementation of these feature. Reflect on the most efficient and detailed prompt.

--------------------

You are an expert C++/Qt5 developer targeting Debian 12.
Create a single CMake-based Qt5 Widgets project named “UsbKeyTool”.
1 Project skeleton
CMake ≥ 3.24
C++20, position-independent code, warnings-as-errors (-Wall -Wextra -pedantic)
Out-of-source build directory ./build
Install into /opt/UsbKeyTool with a .desktop file and an icon.
2 External dependencies (Debian packages)
libudev-dev
libusb-1.0-0-dev
libudisks2-dev
pkg-config
udisks2 (runtime)
gparted (optional runtime helper)
dfu-util (bundled or required at runtime for DFU flashing)
3 GitHub projects to embed or use as reference
libusb https://github.com/libusb/libusb
qt-dfu https://github.com/victronenergy/qt-dfu (DFU flashing GUI pieces)
UDisks2-Qt https://github.com/KDE/solid (KDE Solid wraps UDisks2)
chipid-usb https://github.com/0xFireball/chipid-usb (USB chipset identification)
fwupd https://github.com/fwupd/fwupd (for DFU & metadata ideas)
usbutils https://github.com/gregkh/usbutils (lsusb core parsing)
You may copy small, self-contained files (MIT/BSD licensed) from these repos into src/3rdparty/ and mention their LICENSE files.
4 Feature list & user stories
US-1 “When I plug a USB key the UI immediately lists VendorID, ProductID, Manufacturer, Product string, Serial, bcdDevice, USB version, chipset name (if known), and whether it exposes a mass-storage interface or a DFU interface.”
US-2 “I can click ‘Save Firmware…’ to dump the entire firmware (for devices that allow this via control-in or BULK-in) to a file.”
US-3 “I can click ‘Flash Firmware…’, pick a .bin/.dfu file, and the tool performs the correct DFU_DNLOAD sequence or vendor-specific flashing sequence with progress bar.”
US-4 “I can format the mass-storage part: choose partition table type (MBR/GPT), file-system (FAT32/exFAT/ext4), volume label, and quick vs full wipe.”
US-5 “After formatting the tool asks UDisks2 to mount the new partition and opens it in the default file manager.”
US-6 “All destructive operations require a confirmation dialog and a second “type YES” safeguard.”
US-7 “All actions are logged to ~/.local/share/UsbKeyTool/log.txt with millisecond timestamps.”
5 Architecture
Use libusb-1.0 synchronous API wrapped in a small RAII class UsbHandle.
Use Solid::DeviceNotifier + Solid::Device from KDE Frameworks for hotplug events (this wraps udev & UDisks2).
Use UDisks2 over QtDBus for mount/unmount/format.
Implement a plugin-like “DeviceBackend” interface so future contributors can add vendor-specific flashing algorithms without touching core code.
Provide two built-in backends:
– DfuBackend (standard DFU 1.0/1.1)
– MassStorageBackend (for keys that use SCSI reprogramming commands, e.g. Phison, SMI, Alcor).
All blocking I/O must run inside QThreadPool jobs with progress reported via Qt signals.
6 UI/UX
Single-window QMainWindow with vertical tabs: “Info”, “Firmware”, “Storage”, “Log”.
Dark & light themes via QStyleHints.
Use QProgressBar and QStatusBar for progress and errors.
Use native file dialogs via QFileDialog.
Provide a “View Log” button that tails the log file in a QTextBrowser.
7 Security & permissions
Detect if the process has the necessary polkit privileges (udisks2, fwupd) and show helpful “pkexec …” commands if not.
On first run install a polkit rule (usr/share/polkit-1/actions/com.usbkeytool.pkla) granting active local users the required privileges.
