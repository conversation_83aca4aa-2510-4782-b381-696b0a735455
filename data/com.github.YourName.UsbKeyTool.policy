<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE policyconfig PUBLIC
 "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
 "http://www.freedesktop.org/standards/PolicyKit/1.0/policyconfig.dtd">
<policyconfig>
  <vendor>The UsbKeyTool Project</vendor>
  <vendor_url>https://github.com/YourName/UsbKeyTool</vendor_url>
  <icon_name>com.github.YourName.UsbKeyTool</icon_name>

  <action id="org.freedesktop.udisks2.filesystem-mount-system">
    <description>Mount filesystems for any user</description>
    <message>Authentication is required to mount the device.</message>
    <defaults>
      <allow_any>auth_admin</allow_any>
      <allow_inactive>auth_admin</allow_inactive>
      <allow_active>auth_admin_keep</allow_active>
    </defaults>
  </action>

  <action id="org.freedesktop.udisks2.format-device">
    <description>Format devices for any user</description>
    <message>Authentication is required to format the device.</message>
    <defaults>
      <allow_any>auth_admin</allow_any>
      <allow_inactive>auth_admin</allow_inactive>
      <allow_active>auth_admin_keep</allow_active>
    </defaults>
  </action>

</policyconfig>