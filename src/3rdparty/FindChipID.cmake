# FindChipID.cmake
# Finds the usb.ids file for chipset identification.
#
# This module sets the following variables:
#  CHIPID_FOUND - system has ChipID
#  CHIPID_USB_IDS_FILE - path to the usb.ids file

find_path(CHIPID_USB_IDS_FILE
    NAMES usb.ids
    PATHS
        "${CMAKE_SOURCE_DIR}/src/3rdparty/chipid-usb"
        /usr/share/hwdata
        /usr/share/misc
    DOC "Path to usb.ids file"
)

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(ChipID
    DEFAULT_MSG
    CHIPID_USB_IDS_FILE)

mark_as_advanced(CHIPID_USB_IDS_FILE)